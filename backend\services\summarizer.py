"""Summarizer Service

Provides utility functions to create concise summaries of conversation chunks
using the existing OpenRouter integration. This implements Layer-2 of the
conversational memory architecture.
"""
from __future__ import annotations

from typing import List

from .openrouter_client import chat_completion

SUMMARY_PROMPT = (
    "You are a summarization helper. Given a list of conversation messages, "
    "produce a concise bullet-point summary capturing key intents, decisions, "
    "and clarifications. Use at most 120 words."
)


async def summarize_messages(messages: List[str], model: str = "openrouter/auto") -> str:
    # Build prompt
    joined = "\n".join(messages)
    llm_messages = [
        {"role": "system", "content": SUMMARY_PROMPT},
        {"role": "user", "content": f"MESSAGES:\n{joined}"},
    ]
    try:
        summary = await chat_completion(llm_messages, model=model)
        return summary.strip()
    except Exception as e:
        # Fallback: return truncated content
        return " ".join(joined.split()[:120]) + " …" 