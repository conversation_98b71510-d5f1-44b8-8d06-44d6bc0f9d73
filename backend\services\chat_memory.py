"""Chat Memory Service (Layers 1-3)

Layer 1 – Working memory (recent messages)
Layer 2 – Running compressed summary (string)
Layer 3 – Searchable archive stored in ChromaDB (re-using same DB but different
collection)

Layer 4 (distilled knowledge) will be implemented later.
"""
from __future__ import annotations

from collections import deque
from typing import Deque, List, Dict
import asyncio

from .chroma_manager import <PERSON>roma<PERSON><PERSON>ana<PERSON>, EmbeddingHelper
from .summarizer import summarize_messages
from .distiller import distill_summary

MAX_WORKING_MESSAGES = 15


class ProjectChatMemory:
    def __init__(self, project_id: str):
        self.project_id = project_id
        self.working: Deque[Dict[str, str]] = deque(maxlen=MAX_WORKING_MESSAGES)
        self.summary: str = ""  # naive concatenated summary for now
        self.db = ChromaDBManager(project_id)
        self.embedder = EmbeddingHelper()

    # ------------------------------------------------------------
    # Public API
    # ------------------------------------------------------------

    def add_message(self, role: str, content: str):
        # Normalise content to string for storage
        if not isinstance(content, str):
            import json as _json
            content_str = _json.dumps(content, ensure_ascii=False)[:2000]  # clip large
        else:
            content_str = content

        # Layer 1 – working memory
        self.working.append({"role": role, "content": content_str})

        # Layer 3 – archive
        self.db.add_chat_messages([content_str], metadatas=[{"role": role}])

        # When working memory is full, summarize & compress
        if len(self.working) == MAX_WORKING_MESSAGES:
            # run summarization async in background (fire-and-forget)
            asyncio.create_task(self._compress_working_memory())

        self.db.persist()

    async def _compress_working_memory(self):
        # copy messages
        msgs = [item["content"] for item in self.working]
        summary = await summarize_messages(msgs)
        # move messages into summary
        self.summary += f"\n{summary}"
        self.working.clear()

        # Trigger distillation if summary too long
        if len(self.summary.split()) > 800:  # arbitrary threshold
            asyncio.create_task(distill_summary(self.project_id, self.summary))

    def get_working_context(self) -> List[Dict[str, str]]:
        return list(self.working)

    def get_working_text(self) -> str:
        """Return the last k messages as plain text blocks for prompt injection."""
        lines = []
        for idx, m in enumerate(self.working, start=1):
            lines.append(f"{idx} {m['role']}: {m['content']}")
        return "\n".join(lines)

    def get_summary(self) -> str:
        return self.summary.strip()

    def semantic_search(self, query: str, top_k: int = 5):
        return self.db.query_chat(query, top_k=top_k) 