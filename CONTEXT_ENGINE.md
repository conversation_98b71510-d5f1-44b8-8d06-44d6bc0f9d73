Of course. This is an excellent and crucial next step. A powerful "Context Engine" is the foundation of a truly helpful coding assistant. Moving from a stateless chatbot to a stateful, context-aware agent is a massive leap in capability.

I will research the best possible way to implement this, using the resources you suggested, and then present a detailed implementation plan.

First, I'll start by researching the best open-source embedding models that can run locally with Ollama, specifically for code. This is a critical choice that will determine the quality of the context the AI receives. I will use a web search to find the latest recommendations and benchmarks.
Ran tool

Excellent. The research has yielded a clear winner for the local embedding model and provided strong direction for the overall architecture.

Based on this, here is a comprehensive implementation plan for the **Context Engine**.

### **Implementation Plan: The Context Engine**

This plan outlines a robust, agent-based Retrieval-Augmented Generation (RAG) system that serves as the long-term memory and "second brain" for the Coding Buddy.

#### **1. High-Level Architecture**

We will create a new, specialized agent, the **`ContextAgent`**, whose sole responsibility is to manage the codebase's context. This aligns perfectly with the multi-agent architecture in your implementation plan.

The workflow will be:
1.  **User Prompt**: The user asks a question like, "Where is the services section's javascript handled?"
2.  **Orchestrator**: The `OrchestratorAgent` recognizes this is a code-related question and delegates it to the `CodingAgent`.
3.  **Coding Agent to Context Agent**: The `CodingAgent` realizes it needs codebase context. It formulates a query (e.g., "Find file paths related to 'services javascript'") and sends it to the `ContextAgent`.
4.  **Context Agent in Action**: The `ContextAgent` converts the query into an embedding, searches the project's vector database, and retrieves the most relevant code chunks.
5.  **Response**: The `ContextAgent` returns the retrieved context (file paths, code snippets) to the `CodingAgent`.
6.  **Final Answer**: The `CodingAgent`, now equipped with the relevant code context, generates the final, accurate answer for the user.

Here is a diagram of this new proposed workflow:
graph TD
    subgraph User Interaction
        User[User] -->|Asks about codebase| OrchestratorAgent
    end

    subgraph Agentic Core
        OrchestratorAgent -->|Delegates to| CodingAgent
        CodingAgent -->|Needs context, queries| ContextAgent
        ContextAgent -->|Retrieves context from| VectorStore
        VectorStore -->|Returns relevant chunks| ContextAgent
        ContextAgent -->|Provides context to| CodingAgent
        CodingAgent -->|Generates final answer| OrchestratorAgent
    end

    subgraph Data Layer
        Indexer -- Creates & Updates --> VectorStore(fa:fa-database Vector Store<br/>Project Index)
        Codebase(fa:fa-folder-open Project Files) -- Monitored by --> Indexer
    end

    OrchestratorAgent -->|Presents final answer| User

    style Indexer fill:#D5F5E3
    style ContextAgent fill:#D6EAF8
    style VectorStore fill:#FDEDEC
Ran tool
This model gives us a clear separation of concerns: the `CodingAgent` focuses on reasoning and code generation, while the `ContextAgent` specializes in efficient information retrieval.

#### **2. Core Components & Technology Choices**

*   **Embedding Model (Local & High-Performance):**
    *   **Recommendation:** `nomic-embed-text`
    *   **Why:** My research confirms it's a top-tier, open-source model specifically designed for creating high-quality embeddings. It's available on Ollama, fits your requirements perfectly, and outperforms many well-known alternatives for text encoding tasks. We will configure the backend to use this via Ollama for all indexing and retrieval.

*   **Vector Store (Local & Persistent):**
    *   **Recommendation:** `ChromaDB`
    *   **Why:** It's a modern, open-source vector database designed for RAG. It's simple to set up, runs locally, and has excellent Python support (`chromadb-client`). We can store the database files directly within each project's directory (e.g., in a `.chroma/` folder), ensuring project isolation.

*   **The Indexer (Efficient & Automated):**
    *   This will be a new service responsible for creating and updating the vector store.
    *   **Initial Indexing:** When a project is loaded for the first time, the Indexer will scan all text-based files, split them into logical chunks (e.g., functions, classes, or paragraphs of text), create an embedding for each chunk using `nomic-embed-text`, and store it in `ChromaDB`.
    *   **Incremental Updates:** Re-indexing an entire project is inefficient. We will use a file watcher (like Python's `watchdog` library) to monitor the project directory for file changes (saves, creations, deletions). When a file is modified, the Indexer will automatically update only the embeddings for that specific file, making the process fast and lightweight.

#### **3. Advanced Conversational Memory (Multi-Layered Approach)

The initial idea of simply passing recent chat history is insufficient. A more robust, multi-layered approach to conversational memory is required, as outlined below. This system distinguishes between short-term conversational flow and long-term, searchable knowledge.

1.  **Layer 1: Working Memory (Immediate Context)**
    *   **Component:** A fast, in-memory message buffer (e.g., a Python `deque`).
    *   **Function:** This buffer will hold the last `k` (e.g., 10-15) messages in their full, original form. This collection is passed with every LLM call to provide immediate context for follow-up questions and maintain a natural conversational flow.

2.  **Layer 2: Compressed Summary (Mid-Term Context)**
    *   **Component:** A summarization service that uses a fast, efficient LLM (e.g., Gemini Flash, or a dedicated local model).
    *   **Function:** As messages age out of the Working Memory buffer, they are passed to this service. It maintains a running, condensed summary of the conversation. This summary can be prepended to the prompt, preserving the gist of longer discussions without consuming the entire context window.

3.  **Layer 3: Searchable Archive (Long-Term Recall)**
    *   **Component:** A dedicated `ChromaDB` vector store collection for conversation history, separate from the codebase index.
    *   **Function:** Each user message and AI response is embedded and stored in this archive. The AI will be given a specific tool (e.g., `search_conversation_history`) to perform semantic searches on past conversations. This allows the agent to answer questions like, *"What did we decide about the authentication flow yesterday?"*

4.  **Layer 4: Distilled Knowledge (Permanent Memory)**
    *   **Component:** A periodic or on-demand "distillation agent."
    *   **Function:** This agent analyzes the conversation summaries from Layer 2 to extract key, durable information: critical decisions, user preferences, architectural patterns, and established facts about the project. This "distilled knowledge" is stored in a simple, structured format (e.g., a project-specific `knowledge.json`). This file can be loaded at the start of any new session to prime the AI with a high-level understanding of the project's state and history.

We should implement both the Code Context engine and this advanced Conversational Memory system to provide a truly stateful and intelligent experience.

#### **4. Implementation Steps**

Here's how we can break down the work:

1.  **Backend Setup:**
    *   Add `chromadb-client` and `watchdog` to `backend/requirements.txt`.
    *   Create a new file `backend/agents/context.py` to house the `ContextAgent`.
    *   Implement the `Indexer` service, likely within `backend/projects/file_ops.py` or a new dedicated file, to handle file parsing, chunking, and embedding.
    *   Create a `ChromaDBManager` class to handle all interactions with the vector store (initialization, adding embeddings, querying).

2.  **Agent Integration:**
    *   Define a new tool for the `CodingAgent` called something like `query_codebase`.
    *   When the `CodingAgent` uses this tool, the `Orchestrator` will route the request to the `ContextAgent`.
    *   The `ContextAgent` will use the `ChromaDBManager` to perform the vector search and return the results.

3.  **Project Loading Workflow:**
    *   Modify the project loading logic (likely in `backend/api/projects.py`). When a project is loaded, check if a `.chroma` directory exists.
    *   If not, trigger a full initial indexing of the project.
    *   In either case, start the `watchdog` file monitor for that project path to handle live updates.

This plan provides a solid foundation for building a powerful and intelligent Context Engine. What do you think of this approach? We can start by setting up the backend dependencies and creating the initial files for the new agent and services.