"""File Watcher Service

Uses `watchdog` to monitor a project directory for file changes (create, modify,
move, delete) and updates the ChromaDB index via the Indexer.

This runs in its own daemon thread so it does not block the FastAPI event loop.
Only one observer per project is started.
"""
from __future__ import annotations

import threading
import time
from pathlib import Path
from typing import Dict

from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

from ..config import PROJECTS_ROOT
from .indexer import Indexer


class _ProjectEventHandler(FileSystemEventHandler):
    def __init__(self, project_id: str):
        super().__init__()
        self.project_id = project_id
        self.indexer = Indexer(project_id)

    def on_modified(self, event):
        if event.is_directory:
            return
        rel_path = Path(event.src_path).relative_to(Path(PROJECTS_ROOT) / self.project_id)
        self.indexer.update_file(rel_path.as_posix())

    # treat created and moved similarly
    on_created = on_modified
    on_moved = on_modified


_observers: Dict[str, Observer] = {}


def start_watching(project_id: str):
    if project_id in _observers:
        return  # already watching

    project_path = Path(PROJECTS_ROOT) / project_id
    project_path.mkdir(parents=True, exist_ok=True)

    event_handler = _ProjectEventHandler(project_id)
    observer = Observer()
    observer.schedule(event_handler, str(project_path), recursive=True)
    observer.daemon = True
    observer.start()

    _observers[project_id] = observer


def stop_watching(project_id: str):
    observer = _observers.pop(project_id, None)
    if observer:
        observer.stop()
        observer.join()


def stop_all():
    for pid in list(_observers.keys()):
        stop_watching(pid) 