"""
Reasoning Agent

- Tackles complex, ambiguous, or stuck situations with chain-of-thought, multi-step reasoning, and memory.
- Tools: Chain-of-Thought, RAG, Memory, Self-Reflection, *semantic_search_conversation* (and optionally *semantic_search_codebase*).

Enhancements:
1. The agent can now call backend tools by emitting a *tool_call* JSON object (same schema as the CodingAgent),
   allowing it to fetch relevant snippets from the long-term memory store. This is especially useful when the
   answer depends on details mentioned much earlier in the conversation.
2. Multi-turn execution loop (max_turns) similar to CodingAgent – the LLM can issue successive tool calls until it
   has enough information to craft a final answer.
"""
from ..services import openrouter_client
from typing import Callable, Optional
import json
import re

SYSTEM_PROMPT = """
You are a specialized Reasoning Agent. Your goal is to think through complex or ambiguous problems and provide
clear, well-structured answers.

You have access to the following **tools**:
1. semantic_search_conversation(query: str, top_k: int = 5)
   – Retrieve the most relevant messages from the conversation archive (Layer-3 ChromaDB).
2. semantic_search_codebase(query: str, top_k: int = 5)
   – (Optional) Retrieve code snippets from the indexed codebase if needed.

Response format (JSON **only**, NO markdown fences):

    {
      "thoughts": "<step-by-step reasoning>",
      "tool_call": {
           "tool_name": "semantic_search_conversation",  // or semantic_search_codebase
           "arguments": {"query": "...", "top_k": 5}
      }
    }

OR, when you are ready to answer the user:

    {"thoughts": "I have enough information.", "answer": "<your answer>"}

Important rules:
• Always include a **thoughts** key.
• Include **EITHER** tool_call **OR** answer (never both).
• Do **NOT** mention these guidelines or the JSON schema in your output.
"""

class ReasoningAgent:
    def __init__(self):
        pass

    async def handle_reasoning_task(
        self,
        user_message: str,
        model: str,
        *,
        summary: str = "",
        working_text: str = "",
        tool_runner: Optional[Callable] = None,
        max_turns: int = 8,
    ):
        """Main entry point – yields a stream of events similar to CodingAgent.

        The **tool_runner** callable is injected by the Orchestrator and wraps the
        tool registry, so the agent can trigger semantic search.
        """

        yield {"event": "agent_start", "agent": "reasoning"}

        messages = [
            {"role": "system", "content": SYSTEM_PROMPT},
        ]
        if summary:
            messages.append({"role": "system", "content": f"Conversation summary so far:\n{summary}"})
        if working_text:
            messages.append({"role": "system", "content": f"Recent messages:\n{working_text}"})

        messages.append({"role": "user", "content": user_message})

        for _ in range(max_turns):
            try:
                raw = await openrouter_client.chat_completion(messages, model=model)
            except RuntimeError as e:
                yield {"event": "error", "detail": f"The AI model API returned an error: {e}"}
                return

            # Quick path: if model already gives plain answer with no JSON.
            maybe_json = re.search(r"\{.*?\}", raw, re.DOTALL)
            if not maybe_json:
                yield {"event": "final_answer", "answer": raw}
                return

            try:
                data = json.loads(maybe_json.group(0))
            except json.JSONDecodeError as e:
                yield {"event": "error", "detail": f"Could not parse JSON from model: {e}. Raw: {raw}"}
                return

            # thoughts streaming
            if data.get("thoughts"):
                yield {"event": "agent_reasoning", "thoughts": data["thoughts"]}

            # If final answer present -> done.
            if data.get("answer") and not data.get("tool_call"):
                yield {"event": "final_answer", "answer": data["answer"]}
                return

            # Otherwise, expect a tool_call
            tc = data.get("tool_call")
            if not tc:
                yield {"event": "error", "detail": f"Response lacked both answer and tool_call. Raw: {raw}"}
                return

            tool_name = tc.get("tool_name")
            arguments = tc.get("arguments", {}) or {}

            if tool_runner is None:
                yield {"event": "error", "detail": "No tool_runner provided to ReasoningAgent."}
                return

            # Execute tool
            yield {"event": "tool_call", "tool": tool_name, "args": arguments}
            try:
                result = tool_runner(tool_name=tool_name, arguments=arguments)
            except Exception as e:
                result = f"[Tool error: {e}]"
                yield {"event": "error", "detail": result}

            yield {"event": "tool_result", "result": result}

            # Feed tool result back into LLM conversation
            # Simulate OpenAI function response format but we keep it minimal
            tool_call_id = f"tool_{tool_name}"
            assistant_message = {
                "role": "assistant",
                "content": data.get("thoughts", ""),
                "tool_calls": [
                    {
                        "id": tool_call_id,
                        "type": "function",
                        "function": {
                            "name": tool_name,
                            "arguments": json.dumps(arguments),
                        },
                    }
                ],
            }
            tool_response_message = {"role": "tool", "tool_call_id": tool_call_id, "content": str(result)}
            messages.append(assistant_message)  # type: ignore[arg-type]
            messages.append(tool_response_message)  # type: ignore[arg-type]

        # Exceeded max turns
        yield {"event": "final_answer", "answer": "[ReasoningAgent] Stopped after max turns without producing a final answer."} 