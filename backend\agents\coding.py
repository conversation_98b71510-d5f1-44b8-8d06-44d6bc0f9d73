"""
Coding Agent

- Handles code generation, editing, refactoring, and file operations.
- Tools: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Syntax Checker.
"""
from ..services import openrouter_client
from .schema import LLMResponse
from .tool_registry import run_tool
from typing import Callable
import json
import re
import uuid

SYSTEM_PROMPT = """
You are a specialized Coding Agent. Your goal is to complete the user's coding-related request.
!! STRICT: Never output <|tool_* markers> or any OpenAI tool-calling syntax. Only use the JSON schema below.
You can use tools to interact with the file system.

First, think step-by-step and describe your plan in a "thoughts" field.
Then, based on your plan, choose a tool or an answer.

Your response MUST be a single JSON object. The JSON object should contain a "thoughts" key with your reasoning, and EITHER a "tool_call" key OR an "answer" key.

If you want to use a standard tool (`read_file`, `list_dir`, `read_lines`), your JSON response should look like this: 
{"thoughts": "I need to read the file to see its contents.", "tool_call": {"tool_name": "read_file", "arguments": {"rel_path": "..."}}}

If you want to use semantic search over the codebase, use:
{"thoughts": "I want to find relevant code using semantic search.", "tool_call": {"tool_name": "semantic_search_codebase", "arguments": {"query": "...", "top_k": 5}}}

If you want to WRITE a file, you MUST use a special format:
1. A single line of JSON containing your "thoughts" and "tool_call".
2. The exact delimiter string "---CODE-BLOCK---" on a new line.
3. The raw code content for the file.

EXAMPLE for `write_file`:
{"thoughts": "I will create a basic HTML structure for the dog walking business.", "tool_call": {"tool_name": "write_file", "arguments": {"rel_path": "dog_walking_business.html"}}}
---CODE-BLOCK---
<!DOCTYPE html>
<html lang="en">
<head>
    <title>Paws on the Go</title>
</head>
<body>
    <h1>Welcome!</h1>
</body>
</html>

If you believe the task is complete, or you want to respond directly to the user, use the "answer" key:
{"thoughts": "I have finished the task and will now inform the user.", "answer": "I have successfully created the file."}

Available tools:
- read_file(rel_path: str)
- list_dir(rel_path: str = "")
- read_lines(rel_path: str, start_line: int, end_line: int)
- write_file(rel_path: str, content: str)
- query_codebase(query: str, top_k: int = 5)
- semantic_search_codebase(query: str, top_k: int = 5)
"""


class CodingAgent:
    def __init__(self):
        pass

    async def handle_coding_task(self, user_message: str, model: str, tool_runner: Callable):
        yield {"event": "agent_start", "agent": "coding"}
        messages = [
            {"role": "system", "content": SYSTEM_PROMPT},
            {"role": "user", "content": user_message},
        ]

        max_turns = 10
        for _ in range(max_turns):
            try:
                raw = await openrouter_client.chat_completion(messages, model=model)
            except RuntimeError as e:
                yield {"event": "error", "detail": f"The AI model API returned an error: {e}"}
                return

            data = None
            # Try to parse the special write_file format first
            if "---CODE-BLOCK---" in raw:
                try:
                    json_part_raw, code_part = raw.split("---CODE-BLOCK---", 1)
                    
                    # Find the JSON block within the conversational text
                    json_str = None
                    match = re.search(r'\{.*\}', json_part_raw, re.DOTALL)
                    if not match:
                        raise ValueError("Could not find JSON part in the response before ---CODE-BLOCK---")
                    
                    json_str = match.group(0)
                    data = json.loads(json_str)

                    if data.get("tool_call", {}).get("tool_name") == "write_file":
                        data["tool_call"]["arguments"]["content"] = code_part.strip()
                except (ValueError, json.JSONDecodeError) as e:
                    yield {"event": "error", "detail": f"Error parsing custom write_file format: {e}. Raw: {raw}"}
                    return
            
            # If it's not the special format, try standard JSON parsing
            if data is None:
                json_str = None
                match = re.search(r"```json\s*(\{.*?\})\s*```", raw, re.DOTALL)
                if match:
                    json_str = match.group(1)
                else:
                    json_match = re.search(r"\{.*\}", raw, re.DOTALL)
                    if json_match:
                        json_str = json_match.group(0)
                
                if json_str:
                    try:
                        data = json.loads(json_str)
                    except Exception as e:
                        # Before failing, let's check if it's a direct answer without JSON
                        if not json_str.strip().startswith('{'):
                            yield {"event": "final_answer", "answer": raw}
                            return
                        yield {"event": "error", "detail": f"Error parsing LLM output: {e}. Raw: {raw}"}
                        return

            # If we still have no structured data, it's a direct answer
            if data is None:
                yield {"event": "final_answer", "answer": raw}
                return

            # Now, we have data. Let's process it.
            if data.get("thoughts"):
                yield {"event": "agent_reasoning", "thoughts": data.get("thoughts")}

            try:
                resp = LLMResponse.from_json(data)
            except Exception as e:
                yield {"event": "error", "detail": f"Error creating response object from parsed data: {e}. Data: {data}"}
                return

            if resp.answer:
                yield {"event": "final_answer", "answer": resp.answer}
                return
            
            if resp.tool_call:
                tool_call_id = f"tc_{uuid.uuid4()}"
                
                assistant_message = {
                    "role": "assistant",
                    "content": data.get("thoughts"),
                    "tool_calls": [{
                        "id": tool_call_id,
                        "type": "function",
                        "function": {
                            "name": resp.tool_call.tool_name,
                            "arguments": json.dumps(dict(resp.tool_call.arguments))
                        }
                    }]
                }
                messages.append(assistant_message)

                # The orchestrator handles the 'write_file' pseudo-tool.
                # The coding agent's job is to propose the content.
                if resp.tool_call.tool_name == 'write_file':
                    args = resp.tool_call.arguments
                    yield {
                        "event": "file_edit",
                        "file_edit": {"rel_path": args.get('rel_path'), "content": args.get('content')}
                    }
                    yield {
                        "event": "final_answer",
                        "answer": f"I have proposed changes for `{args.get('rel_path', 'a file')}`. Please review them."
                    }
                    return

                tool_args = dict(resp.tool_call.arguments)
                yield {"event": "tool_call", "tool": resp.tool_call.tool_name, "args": tool_args}
                try:
                    tool_result = tool_runner(tool_name=resp.tool_call.tool_name, arguments=tool_args)
                    yield {"event": "tool_result", "result": tool_result}
                except Exception as e:
                    print(f"Error executing tool {resp.tool_call.tool_name}: {e}")
                    tool_result = f"[Tool error: {e}]"
                    yield {"event": "error", "detail": tool_result}
                
                messages.append({
                    "role": "tool", 
                    "tool_call_id": tool_call_id,
                    "content": str(tool_result)
                })
            elif resp.answer is None:
                yield {"event": "error", "detail": f"LLM response was valid JSON but had no 'answer' or 'tool_call'. Raw: {raw}"}
                return

        yield {"event": "final_answer", "answer": "[Agent stopped after reaching max turns]"} 