"""
Orchestrator Agent

- Receives user requests and decomposes them into subtasks.
- Delegates subtasks to specialized agents (coding, research, reasoning, debug, ask-user, etc.).
- Aggregates results, manages workflow, and ensures security/approval checkpoints.
"""

from .coding import CodingAgent
from .research import ResearchAgent
from .reasoning import ReasoningAgent
from .ask_user import AskUserAgent
from .context import ContextAgent
from ..services import openrouter_client
from .schema import LLMResponse, ToolCall
from .tool_registry import run_tool
from langchain.prompts import PromptTemplate
from functools import partial, lru_cache
from typing import Callable
import json
import re
from .. import audit
from ..services.chat_memory import ProjectChatMemory

SYSTEM_PROMPT = """
You are a master orchestrator agent. Your job is to receive a user request and delegate it to the most appropriate specialized agent.

Based on the user's request **and the recent conversation context**, choose one of the following agents to handle the task:

- 'coding': For any tasks related to writing, reading, or modifying code or files.
- 'research': For tasks that require searching the web or reading documentation.
- 'reasoning': For complex, ambiguous, or abstract questions.
- 'ask_user': When you need to ask the user for clarification.
- 'context': For questions that require searching the project codebase or retrieving code snippets.

Guidelines:
• If the user asks to *explain, clarify, or elaborate* on something from the **immediately-preceding assistant message**, it's usually a reasoning task.
• If the user's request explicitly mentions a file path (e.g. contains a ".py", ".js", ".md", etc. or an "@/path/to/file") or references a specific line/lines in a file, it's almost certainly a context task because the answer depends on reading project files first.
• When in doubt between context and reasoning, prefer **context first**, then follow-up with reasoning if needed.
• When in doubt between asking the user or reasoning, prefer reasoning.

Respond ONLY with a JSON object with a single key, "agent", whose value is the name of the selected agent.
For example:
{"agent": "coding"}
"""

@lru_cache(maxsize=32)
def _get_memory(project_id: str) -> ProjectChatMemory:
    return ProjectChatMemory(project_id)

class OrchestratorAgent:
    def __init__(self):
        self.coding_agent = CodingAgent()
        self.research_agent = ResearchAgent()
        self.reasoning_agent = ReasoningAgent()
        self.ask_user_agent = AskUserAgent()
        self.context_agent = ContextAgent
        # Structured conversational state
        self.state = {
            "last_file": None,
            "last_line": None,
            "last_code_snippet": None,
            "last_agent_action": None,
            "last_tool_result": None,
            "last_topic": None,
            "user_preferences": {},
            "distilled_knowledge": {},
        }

    async def handle_request_stream(self, user_message: str, project_id: str = "demo", model: str = "openrouter/auto"):
        """
        Handles the user request by delegating to the appropriate agent and streams back the events.
        """
        memory = _get_memory(project_id)
        memory.add_message("user", user_message)

        yield {"event": "agent_start", "agent": "orchestrator"}

        messages = [
            {"role": "system", "content": SYSTEM_PROMPT},
            {"role": "system", "content": f"Conversation summary so far:\n{memory.get_summary()}"},
            {"role": "user", "content": user_message},
        ]

        raw = await openrouter_client.chat_completion(messages, model=model)
        
        try:
            response_json = json.loads(raw)
            agent_name = response_json.get("agent")
        except (json.JSONDecodeError, AttributeError) as e:
            print(f"Could not parse agent routing response: {e}. Raw: {raw}")
            match = re.search(r'["\']agent["\']\s*:\s*["\'](\w+)["\']', raw)
            if match:
                agent_name = match.group(1)
            else:
                yield {"event": "error", "detail": f"Could not determine which agent to use. Raw response: {raw}"}
                return

        yield {"event": "agent_handoff", "agent": agent_name}

        # Create a tool runner with the project_id context baked in
        tool_runner = partial(run_tool, project_id=project_id)

        agent_stream = None
        if agent_name == "coding":
            agent_stream = self.coding_agent.handle_coding_task(user_message, model, tool_runner)
        elif agent_name == "research":
            agent_stream = self.research_agent.handle_research_task(user_message, model, tool_runner)
        elif agent_name == "reasoning":
            agent_stream = self.reasoning_agent.handle_reasoning_task(
                user_message,
                model,
                summary=memory.get_summary(),
                working_text=memory.get_working_text(),
                tool_runner=tool_runner,
            )
        elif agent_name == "ask_user":
            agent_stream = self.ask_user_agent.handle_ask_user_task(user_message, model)
        elif agent_name == "context":
            ctx_agent = self.context_agent(project_id)
            agent_stream = ctx_agent.handle_context_task(user_message, model, tool_runner)
        else:
            yield {"event": "error", "detail": f"Agent '{agent_name}' is not yet implemented."}
            return

        if agent_stream:
            async for event in agent_stream:
                if not isinstance(event, dict):
                    yield event
                    continue
                audit.log_event(project_id, event.get("agent", "orchestrator"), event)

                # --- Update conversational state ---
                # Track last file, line, code, agent action, tool result
                event_type = event.get("event")
                if event_type == "tool_call":
                    tool = event.get("tool")
                    args = event.get("args", {})
                    self.state["last_agent_action"] = f"tool_call:{tool}"
                    if tool in ("read_file", "read_lines", "write_file"):
                        if isinstance(args, dict):
                            self.state["last_file"] = args.get("rel_path")
                            if tool == "read_lines":
                                self.state["last_line"] = args.get("start_line")
                        else:
                            self.state["last_file"] = None
                            if tool == "read_lines":
                                self.state["last_line"] = None
                elif event_type == "tool_result":
                    self.state["last_tool_result"] = event.get("result")
                    # If result is a code snippet or file content, track it
                    result = event.get("result")
                    if isinstance(result, str) and len(result) < 1000:
                        self.state["last_code_snippet"] = result
                elif event_type == "final_answer":
                    memory.add_message("assistant", event.get("answer", ""))
                    self.state["last_agent_action"] = "final_answer"
                # (Extend as needed for more state)
                yield event

    async def handle_request(self, user_message: str, project_id: str = "demo", model: str = "openrouter/auto"):
        """
        Non-streaming version for compatibility. Collects events and returns a single response.
        """
        final_response = {}
        async for event in self.handle_request_stream(user_message, project_id, model):
            if event['event'] == 'final_answer':
                final_response['answer'] = event['answer']
            elif event['event'] == 'file_edit':
                final_response['file_edit'] = event['file_edit']
        return final_response 