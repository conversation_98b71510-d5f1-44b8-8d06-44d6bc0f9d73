import React, { useState, useEffect } from 'react';
import FileExplorer from './components/FileExplorer';
import EditorTabs from './components/EditorTabs';
import ChatPanel from './components/ChatPanel';
import TerminalPanel from './components/TerminalPanel';
import ProjectSelector from './components/ProjectSelector';
import SettingsModal from './components/SettingsModal';
import AuditLogPanel from './components/AuditLogPanel';
import IndexStatusWidget from './components/IndexStatusWidget';

export default function App() {
  const [selectedFile, setSelectedFile] = useState(null);
  const [projectId, setProjectId] = useState(null);
  const [availableProjects, setAvailableProjects] = useState([]);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [model, setModel] = useState('openrouter/auto');
  const [proposedEdit, setProposedEdit] = useState(null);
  const [auditOpen, setAuditOpen] = useState(false);
  const [terminalOpen, setTerminalOpen] = useState(false);

  // This effect runs when the list of available projects changes
  useEffect(() => {
    // If we have projects but none is selected, default to the first one.
    if (availableProjects.length > 0 && !projectId) {
      setProjectId(availableProjects[0]);
    }
  }, [availableProjects, projectId]);

  return (
    <div style={{ display: 'flex', flexDirection: 'column', height: '100vh' }}>
      <div style={{ padding: 8, borderBottom: '1px solid #ccc', display: 'flex', alignItems: 'center', gap: 16 }}>
        <ProjectSelector 
          projectId={projectId} 
          setProjectId={setProjectId} 
          setAvailableProjects={setAvailableProjects}
        />
        <button onClick={() => setSettingsOpen(true)} style={{ background: 'none', border: 'none', cursor: 'pointer', fontSize: 22 }} title="Settings">
          <span role="img" aria-label="settings">⚙️</span>
        </button>
        <IndexStatusWidget projectId={projectId} />
      </div>
      <SettingsModal open={settingsOpen} onClose={() => setSettingsOpen(false)} model={model} setModel={setModel} />
      
      {projectId ? (
        <div style={{ display: 'flex', flex: 1, minHeight: 0, height: '100%' }}>
          <FileExplorer onSelectFile={setSelectedFile} projectId={projectId} />
          <div style={{ flex: 1, display: 'flex', flexDirection: 'column', height: '100%', overflow: 'hidden' }}>
            <div style={{
              flex: terminalOpen ? '1 1 auto' : '1 1 100%',
              minHeight: terminalOpen ? '200px' : '300px',
              display: 'flex',
              flexDirection: 'column',
              overflow: 'hidden'
            }}>
              <EditorTabs
                selectedFile={selectedFile}
                projectId={projectId}
                proposedEdit={proposedEdit}
                onEditApplied={() => setProposedEdit(null)}
                terminalOpen={terminalOpen}
              />
            </div>
            <div onClick={() => setTerminalOpen(o => !o)}
                 style={{
                   height: 24,
                   cursor: 'pointer',
                   background: '#eee',
                   borderTop: '1px solid #ccc',
                   display: 'flex',
                   alignItems: 'center',
                   paddingLeft: 8,
                   userSelect: 'none',
                   flexShrink: 0,
                   zIndex: 10
                 }}>
              {terminalOpen ? '▼ Terminal (click to hide)' : '▲ Terminal (click to open)'}
            </div>
            {terminalOpen && (
              <div style={{
                height: '300px',
                display: 'flex',
                flexDirection: 'column',
                borderTop: '1px solid #ccc',
                flexShrink: 0,
                backgroundColor: '#1e1e1e'
              }}>
                <TerminalPanel projectId={projectId} />
              </div>
            )}
          </div>
          <div style={{ width: '30%', borderLeft: '1px solid #ccc', display: 'flex', flexDirection: 'column', minHeight: 0, height: '100%' }}>
            <div style={{ padding: '4px 8px', borderBottom: '1px solid #ddd', display: 'flex', justifyContent: 'flex-end' }}>
              <button onClick={() => setAuditOpen(o => !o)} style={{ fontSize: 12, padding: '2px 6px' }}>
                {auditOpen ? '✖ Close Log' : '🕵 Audit Log'}
              </button>
            </div>
            <div style={{ flex: 1, minHeight: 0 }}>
              <ChatPanel model={model} project={projectId} onProposeEdit={setProposedEdit} />
            </div>
            {auditOpen && <AuditLogPanel project={projectId} open={auditOpen} />}
          </div>
        </div>
      ) : (
        <div style={{ flex: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <h2>Please select or create a project to begin.</h2>
        </div>
      )}
    </div>
  );
} 