import { EventSourcePolyfill } from 'event-source-polyfill';

const BASE_URL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8000';
const PROJECT_ID = 'demo'; // temporary default

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000';
// Convert http to ws
const WS_BASE_URL = API_BASE_URL.replace(/^http/, 'ws');

async function request(url, options = {}) {
  console.log(`[API Request] Starting fetch for: ${url}`, options);
  try {
    const res = await fetch(url, options);
    console.log(`[API Request] Response status: ${res.status} for ${url}`);
    if (!res.ok) {
      const data = await res.json().catch(() => ({ detail: "Response was not valid JSON." }));
      console.error(`[API Request] Error: Request failed with status ${res.status}`, data);
      throw new Error(data.detail || `Request failed: ${res.status}`);
    }
    const data = await res.json();
    console.log(`[API Request] Successfully parsed JSON response for ${url}`, data);
    return data;
  } catch (error) {
    console.error(`[API Request] A critical network or fetch error occurred for ${url}`, error);
    throw error; // Re-throw the error to be caught by the component
  }
}

export async function listDir(path = '', projectId = 'demo') {
  const url = `${BASE_URL}/api/projects/${projectId}/list/${encodeURIComponent(path)}`;
  return request(url);
}

export async function readFile(path, projectId = 'demo') {
  const url = `${BASE_URL}/api/projects/${projectId}/files/${encodeURIComponent(path)}`;
  return request(url);
}

export async function writeFile(path, content, approved = true, projectId = 'demo') {
  const url = `${BASE_URL}/api/projects/${projectId}/files/${encodeURIComponent(path)}`;
  return request(url, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ content, approved }),
  });
}

export async function runCommand(command) {
  const url = `${BASE_URL}/api/terminal/run`;
  return request(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ command }),
  });
}

export const listProjects = async () => {
  const url = `${BASE_URL}/api/projects/list`;
  return request(url);
};

export const createProject = async (project_id) => {
  const url = `${BASE_URL}/api/projects/create`;
  return request(url, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ project_id }),
  });
};

export const agentChat = (req, onEvent) => {
  const ws = new WebSocket(`${WS_BASE_URL}/api/chat/ws`);

  ws.onopen = () => {
    console.log("WebSocket connection established");
    ws.send(JSON.stringify(req));
  };

  ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    onEvent(data);
  };

  ws.onerror = (err) => {
    console.error("WebSocket error:", err);
    onEvent({ event: 'error', detail: 'WebSocket connection failed.' });
  };

  ws.onclose = (event) => {
    if (event.wasClean) {
      console.log(`WebSocket connection closed cleanly, code=${event.code} reason=${event.reason}`);
    } else {
      console.error('WebSocket connection died');
      onEvent({ event: 'error', detail: 'WebSocket connection died.' });
    }
  };

  return ws; // Return the socket so the component can close it if needed
};

export const getAllProjectFiles = async (projectId) => {
  console.log(`[API Service] getAllProjectFiles called with projectId: '${projectId}'`);
  const url = `${BASE_URL}/api/projects/${projectId}/files`;
  return request(url);
};

export const getAuditEvents = async (projectId, limit = 100, offset = 0) => {
  const url = `${BASE_URL}/api/audit/${projectId}?limit=${limit}&offset=${offset}`;
  return request(url);
};

export async function getIndexStatus(projectId = 'demo') {
  const url = `${BASE_URL}/api/projects/${projectId}/index/status`;
  return request(url);
}

export async function triggerDistill(projectId = 'demo') {
  const url = `${BASE_URL}/api/projects/${projectId}/distill`;
  return request(url, { method: 'POST' });
} 