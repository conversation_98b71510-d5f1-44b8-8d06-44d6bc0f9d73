from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Optional
import os
from ..config import PROJECTS_ROOT
from ..projects import file_ops
from ..services.chroma_manager import ChromaDBManager
from ..services.chat_memory import ProjectChatMemory
from ..services.distiller import distill_summary

router = APIRouter(prefix="/api/projects", tags=["projects"])


class FileContent(BaseModel):
    content: str
    approved: Optional[bool] = True  # future flag for approval workflow


class ProjectInfo(BaseModel):
    project_id: str


@router.get("/{project_id}/list/{path:path}")
async def list_directory(project_id: str, path: str = "") -> List[Dict]:
    try:
        return file_ops.list_dir(project_id, path)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/{project_id}/files")
async def get_all_project_files(project_id: str) -> List[str]:
    """Recursively get all file paths for a project."""
    try:
        return file_ops.list_all_files(project_id)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {e}")


@router.get("/{project_id}/files/{path:path}")
async def read_file(project_id: str, path: str):
    try:
        content = file_ops.read_file(project_id, path)
        return {"path": path, "content": content}
    except (ValueError, FileNotFoundError) as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/{project_id}/files/{path:path}")
async def write_file(project_id: str, path: str, file: FileContent):
    try:
        file_ops.write_file(project_id, path, file.content, approved=file.approved)
        return {"status": "success"}
    except NotImplementedError as e:
        raise HTTPException(status_code=501, detail=str(e))
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/list", response_model=List[ProjectInfo])
def list_projects():
    try:
        projects = [
            d for d in os.listdir(PROJECTS_ROOT) 
            if os.path.isdir(os.path.join(PROJECTS_ROOT, d)) and not d.startswith('@')
        ]
        return [ProjectInfo(project_id=p) for p in projects]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


class CreateProjectRequest(BaseModel):
    project_id: str


@router.post("/create", response_model=ProjectInfo)
def create_project(req: CreateProjectRequest):
    project_path = os.path.join(PROJECTS_ROOT, req.project_id)
    if os.path.exists(project_path):
        raise HTTPException(status_code=400, detail="Project already exists")
    try:
        os.makedirs(project_path)
        return ProjectInfo(project_id=req.project_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{project_id}/index/status")
async def index_status(project_id: str):
    try:
        db = ChromaDBManager(project_id)
        code_count = db._collection(ChromaDBManager.CODE_COLLECTION).count()
        chat_count = db._collection(ChromaDBManager.CHAT_COLLECTION).count()
        return {"code_chunks": code_count, "chat_messages": chat_count}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{project_id}/distill")
async def trigger_distill(project_id: str):
    try:
        mem = ProjectChatMemory(project_id)
        summary = mem.get_summary()
        data = await distill_summary(project_id, summary)
        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 