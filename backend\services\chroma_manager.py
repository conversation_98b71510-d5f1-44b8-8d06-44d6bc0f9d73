"""ChromaDBManager

Utility layer around ChromaDB for storing and retrieving embeddings for:
1. Codebase chunks (collection: `code_chunks`)
2. Conversation history (collection: `chat_messages`)

The class takes a `project_id` so that each project gets its own isolated
persistent Chroma database under `<PROJECT_ROOT>/.chroma`.

Dependencies:
    pip install chromadb

Note: This module purposefully avoids any heavy framework coupling. It can be
used from agents, tools, or background jobs.
"""
from __future__ import annotations

import os
import hashlib
from pathlib import Path
from typing import List, Sequence, Dict, Any

import chromadb
try:
    from chromadb import PersistentClient  # new API >=0.4.0
except ImportError:
    PersistentClient = None

try:
    from chromadb.config import Settings  # for legacy client
except ImportError:
    Settings = None
from chromadb.api.types import QueryResult

from ..config import PROJECTS_ROOT

# ---- Embedding helper ------------------------------------------------------

import httpx
import json


class EmbeddingHelper:
    """Minimal wrapper around the Ollama embeddings HTTP endpoint.

    We use the `nomic-embed-text` model by default. Feel free to override in
    the future via the `model` argument.
    """

    def __init__(self, model: str = "nomic-embed-text", host: str = "http://localhost:11434"):
        self.model = model
        # Ensure no trailing slash for consistency
        self.base_url = host.rstrip("/")

    async def embed_async(self, texts: Sequence[str]) -> List[List[float]]:
        """Async embedding generation. Requires an async HTTP client."""
        async with httpx.AsyncClient(timeout=30.0) as client:
            results: List[List[float]] = []
            for text in texts:
                payload = {"model": self.model, "prompt": text}
                resp = await client.post(f"{self.base_url}/api/embeddings", json=payload)
                resp.raise_for_status()
                data = resp.json()
                # Response schema: { "embedding": [...] }
                embedding = data.get("embedding")
                if embedding is None:
                    raise RuntimeError(f"Unexpected embedding response: {data}")
                results.append(embedding)
            return results

    def embed(self, texts: Sequence[str]) -> List[List[float]]:
        """Sync wrapper calling the same Ollama endpoint."""
        client = httpx.Client(timeout=30.0)
        results: List[List[float]] = []
        for text in texts:
            payload = {"model": self.model, "prompt": text}
            resp = client.post(f"{self.base_url}/api/embeddings", json=payload)
            resp.raise_for_status()
            data = resp.json()
            embedding = data.get("embedding")
            if embedding is None:
                raise RuntimeError(f"Unexpected embedding response: {data}")
            results.append(embedding)
        client.close()
        return results


# ---- ChromaDB Manager ------------------------------------------------------


class ChromaDBManager:
    """Lightweight wrapper around chromadb.Client scoped to a project."""

    CODE_COLLECTION = "code_chunks"
    CHAT_COLLECTION = "chat_messages"

    def __init__(self, project_id: str):
        self.project_id = project_id
        self._client = None  # lazy init

    # ---------------------------------------------------------------------
    # Private helpers
    # ---------------------------------------------------------------------

    def _project_path(self) -> Path:
        return Path(PROJECTS_ROOT) / self.project_id

    def _db_path(self) -> Path:
        return self._project_path() / ".chroma"

    def _client_instance(self) -> chromadb.Client:
        if self._client is None:
            db_path = self._db_path()
            db_path.mkdir(parents=True, exist_ok=True)
            if PersistentClient is not None:
                self._client = PersistentClient(path=str(db_path))
            else:
                # Legacy fallback
                if Settings is None:
                    raise RuntimeError("Chroma version incompatible – please upgrade or pin <0.4.0")
                self._client = chromadb.Client(Settings(
                    chroma_db_impl="duckdb+parquet",
                    persist_directory=str(db_path),
                ))
        return self._client

    def _collection(self, name: str):
        return self._client_instance().get_or_create_collection(name)

    # ------------------------------------------------------------------
    # Public API
    # ------------------------------------------------------------------

    def add_documents(self, collection: str, documents: Sequence[str], metadatas: Sequence[Dict[str, Any]] | None = None, ids: Sequence[str] | None = None):
        if ids is None:
            # Stable ids derived from hash of content to avoid duplicates
            ids = [hashlib.sha1(doc.encode("utf-8")).hexdigest() for doc in documents]
        self._collection(collection).add(documents=list(documents), metadatas=list(metadatas or ({},) * len(documents)), ids=list(ids))

    def upsert_documents(self, collection: str, documents: Sequence[str], metadatas: Sequence[Dict[str, Any]] | None = None, ids: Sequence[str] | None = None):
        if ids is None:
            ids = [hashlib.sha1(doc.encode("utf-8")).hexdigest() for doc in documents]
        self._collection(collection).upsert(documents=list(documents), metadatas=list(metadatas or ({},) * len(documents)), ids=list(ids))

    def query(self, collection: str, query_text: str, top_k: int = 5) -> QueryResult:
        # Embed query text
        embedder = EmbeddingHelper()
        embedding = embedder.embed([query_text])[0]
        return self._collection(collection).query(query_embeddings=[embedding], n_results=top_k)

    # Convenience wrappers

    def add_code_chunks(self, documents: Sequence[str], metadatas: Sequence[Dict[str, Any]] | None = None, ids: Sequence[str] | None = None):
        self.add_documents(self.CODE_COLLECTION, documents, metadatas, ids)

    def query_code(self, query_text: str, top_k: int = 5) -> QueryResult:
        return self.query(self.CODE_COLLECTION, query_text, top_k)

    def add_chat_messages(self, messages: Sequence[str], metadatas: Sequence[Dict[str, Any]] | None = None, ids: Sequence[str] | None = None):
        self.add_documents(self.CHAT_COLLECTION, messages, metadatas, ids)

    def query_chat(self, query_text: str, top_k: int = 5) -> QueryResult:
        return self.query(self.CHAT_COLLECTION, query_text, top_k)

    def persist(self):
        if self._client is not None and hasattr(self._client, "persist"):
            # Legacy client (<0.4) supports explicit persist()
            try:
                self._client.persist()
            except Exception:
                pass  # ignore errors for newer client
        # PersistentClient ≥0.4 persists automatically 