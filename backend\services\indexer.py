"""Indexer Service

Responsible for building and maintaining the ChromaDB index for a given
project's codebase. This can be triggered manually (full rebuild) or called
incrementally when individual files change.

Note: The current implementation performs *very simple* chunking (splitting
files every N lines). You may improve this later by using language-aware
chunking (e.g., tree-sitter) or token-based splitting.
"""
from __future__ import annotations

import os
from pathlib import Path
from typing import List, Dict

from .chroma_manager import ChromaDBManager, EmbeddingHelper
from ..projects import file_ops
from ..config import PROJECTS_ROOT

DEFAULT_CHUNK_SIZE_LINES = 50


def _chunk_text(text: str, max_lines: int = DEFAULT_CHUNK_SIZE_LINES) -> List[str]:
    """Naively split a string into chunks of at most `max_lines` lines."""
    lines = text.splitlines()
    chunks = []
    for i in range(0, len(lines), max_lines):
        chunk = "\n".join(lines[i : i + max_lines])
        chunks.append(chunk)
    return chunks


class Indexer:
    def __init__(self, project_id: str):
        self.project_id = project_id
        self.manager = ChromaDBManager(project_id)
        self.embedder = EmbeddingHelper()

    # ------------------------------------------------------------------
    # Public methods
    # ------------------------------------------------------------------

    def full_index(self):
        """Index *all* text-based files in the project."""
        all_files = file_ops.list_all_files(self.project_id)
        for rel_path in all_files:
            self._index_single_file(rel_path)
        self.manager.persist()

    def update_file(self, rel_path: str):
        """(Re-)index a single file after it has changed."""
        self._index_single_file(rel_path, upsert=True)
        self.manager.persist()

    # ------------------------------------------------------------------
    # Internal helpers
    # ------------------------------------------------------------------

    def _index_single_file(self, rel_path: str, *, upsert: bool = False):
        # Read file content
        try:
            content = file_ops.read_file(self.project_id, rel_path)
        except FileNotFoundError:
            return  # Ignore if file was removed between list and read

        # Basic check – skip binary files by presence of non-text bytes
        if "\x00" in content:
            return

        chunks = _chunk_text(content)
        metadatas: List[Dict[str, str]] = [{"path": rel_path, "chunk": str(idx)} for idx, _ in enumerate(chunks)]

        # Generate embeddings
        embeddings = self.embedder.embed(chunks)

        # Upsert into Chroma
        collection = self.manager._collection(ChromaDBManager.CODE_COLLECTION)
        if upsert:
            collection.upsert(
                documents=chunks,
                embeddings=embeddings,
                metadatas=metadatas,
            )
        else:
            collection.add(
                documents=chunks,
                embeddings=embeddings,
                metadatas=metadatas,
            ) 