import React, { useEffect, useState } from 'react';
import { getIndexStatus, triggerDistill } from '../services/api';
import './IndexStatusWidget.css';

export default function IndexStatusWidget({ projectId = 'demo' }) {
  const [status, setStatus] = useState({ code_chunks: 0, chat_messages: 0 });
  const [loading, setLoading] = useState(false);
  const [distilling, setDistilling] = useState(false);

  const fetchStatus = async () => {
    setLoading(true);
    try {
      const data = await getIndexStatus(projectId);
      setStatus(data);
    } catch (e) {
      console.error('Failed to fetch index status', e);
    } finally {
      setLoading(false);
    }
  };

  const handleDistill = async () => {
    setDistilling(true);
    try {
      await triggerDistill(projectId);
      alert('Distillation complete. knowledge.json updated.');
    } catch (e) {
      alert('Distillation failed');
    } finally {
      setDistilling(false);
    }
  };

  useEffect(() => {
    fetchStatus();
    const id = setInterval(fetchStatus, 10000); // refresh every 10s
    return () => clearInterval(id);
  }, [projectId]);

  return (
    <div className="index-status-widget">
      <span className="index-status-label">Index:</span>
      {loading ? (
        <span>Loading…</span>
      ) : (
        <span className="index-status-values">
          {status.code_chunks} code chunks · {status.chat_messages} messages
        </span>
      )}
      <button className="distill-btn" disabled={distilling} onClick={handleDistill}>
        {distilling ? 'Distilling…' : 'Distill'}
      </button>
    </div>
  );
} 