import React, { useState, useEffect, useRef } from 'react';
import { readFile, writeFile } from '../services/api';
import Editor, { DiffEditor } from '@monaco-editor/react';
import { useHotkeys } from 'react-hotkeys-hook';
import NiceModal from '@ebay/nice-modal-react';
import SaveDiffModal from './SaveDiffModal';

export default function EditorTabs({ selectedFile, projectId, proposedEdit, onEditApplied, terminalOpen }) {
  const [tabs, setTabs] = useState([]);
  const [activePath, setActivePath] = useState(null);
  const editorRef = useRef(null);

  React.useEffect(() => {
    if (selectedFile) {
      openFile(selectedFile);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedFile, projectId]);

  React.useEffect(() => {
    if (proposedEdit) {
      proposeFileEdit(proposedEdit.rel_path, proposedEdit.content);
      onEditApplied(); // Signal that we've handled it
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [proposedEdit]);

  React.useEffect(() => {
    setTabs([]);
    setActivePath(null);
  }, [projectId]);

  // Trigger editor resize when layout changes
  useEffect(() => {
    const handleResize = () => {
      if (editorRef.current) {
        editorRef.current.layout();
      }
    };

    // Trigger resize after a short delay to allow layout to settle
    const timeoutId = setTimeout(handleResize, 100);

    // Also listen for window resize events
    window.addEventListener('resize', handleResize);

    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('resize', handleResize);
    };
  }, [tabs, activePath, terminalOpen]); // Re-run when tabs, active path, or terminal state changes

  async function openFile(path) {
    // if already open, just activate
    const existing = tabs.find((t) => t.path === path);
    if (existing) {
      setActivePath(path);
      return;
    }
    try {
      const { content } = await readFile(path, projectId);
      setTabs((prev) => [
        ...prev,
        { path, content, savedContent: content, diffMode: false },
      ]);
      setActivePath(path);
    } catch (e) {
      alert(`Failed to open file: ${e.message}`);
    }
  }

  async function proposeFileEdit(path, newContent) {
    const existingTab = tabs.find((t) => t.path === path);
    let originalContent;
    let isNewFile = false;

    if (existingTab) {
      originalContent = existingTab.savedContent;
    } else {
      try {
        const fileData = await readFile(path, projectId);
        originalContent = fileData.content;
      } catch (e) {
        // It's a new file
        originalContent = '';
        isNewFile = true;
      }
    }

    const newTab = {
      path,
      content: newContent,
      savedContent: originalContent,
      diffMode: true,
    };

    // If file isn't open, add it. Otherwise, replace it.
    const otherTabs = tabs.filter((t) => t.path !== path);
    setTabs([...otherTabs, newTab]);
    setActivePath(path);
  }

  function handleChange(value = '') {
    setTabs((prev) =>
      prev.map((t) =>
        t.path === activePath ? { ...t, content: value } : t
      )
    );
  }

  const handleSave = async () => {
    if (!activePath) return;
    const tab = tabs.find((t) => t.path === activePath);
    if (!tab) return;

    // If not in diff mode yet and file is dirty -> open diff view
    if (!tab.diffMode) {
      if (tab.content === tab.savedContent) return; // nothing to save
      setTabs((prev) =>
        prev.map((t) =>
          t.path === tab.path ? { ...t, diffMode: true } : t
        )
      );
      return;
    }

    // In diff mode -> actually persist
    try {
      await writeFile(tab.path, tab.content, true, projectId);
      setTabs((prev) =>
        prev.map((t) =>
          t.path === tab.path
            ? { ...t, savedContent: t.content, diffMode: false }
            : t
        )
      );
    } catch (e) {
      console.error(e);
      alert('Save failed: ' + e.message);
    }
  };

  // Cancel diff -> revert content
  const handleCancel = () => {
    if (!activePath) return;
    setTabs((prev) =>
      prev.map((t) =>
        t.path === activePath
          ? { ...t, content: t.savedContent, diffMode: false }
          : t
      )
    );
  };

  // Ctrl+S hotkey
  useHotkeys('ctrl+s, command+s', (e) => {
    e.preventDefault();
    handleSave();
  }, [tabs, activePath]);

  // ESC to cancel when diffMode
  useHotkeys('esc', () => {
    const tab = tabs.find((t) => t.path === activePath);
    if (tab?.diffMode) handleCancel();
  }, [tabs, activePath]);

  return (
    <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
      <div style={{ display: 'flex', borderBottom: '1px solid #ccc', overflowX: 'auto', alignItems: 'center' }}>
        {tabs.map((tab) => (
          <div
            key={tab.path}
            onClick={() => setActivePath(tab.path)}
            style={{
              padding: '4px 8px',
              cursor: 'pointer',
              backgroundColor: tab.path === activePath ? '#eee' : 'transparent',
            }}
          >
            {tab.path.split('/').pop()}
          </div>
        ))}
        {activePath && (
          <button style={{ marginLeft: 'auto', marginRight: 8 }} onClick={handleSave}>
            Save
          </button>
        )}
      </div>
      <div style={{ flex: 1, position: 'relative' }}>
        {tabs.length > 0 && activePath && (() => {
          const current = tabs.find((t) => t.path === activePath);
          if (!current) return null;
          if (current.diffMode) {
            return (
              <>
                <DiffEditor
                  height="100%"
                  original={current.savedContent}
                  modified={current.content}
                  language="markdown"
                  options={{ renderSideBySide: true, fontSize: 14 }}
                  onChange={(v) => handleChange(v ?? '')}
                />
                <div style={{ position: 'absolute', top: 8, right: 8 }}>
                  <button style={{ marginRight: 8 }} onClick={handleSave}>Save</button>
                  <button onClick={handleCancel}>Cancel</button>
                </div>
              </>
            );
          }
          return (
            <Editor
              height="100%"
              defaultLanguage="markdown"
              value={current.content}
              onChange={(v) => handleChange(v ?? '')}
              options={{ fontSize: 14 }}
              onMount={(editor) => {
                editorRef.current = editor;
              }}
            />
          );
        })()}
      </div>
    </div>
  );
} 