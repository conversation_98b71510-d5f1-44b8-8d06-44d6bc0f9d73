# Coding Buddy AI Agent – Implementation Plan

## 🚨 2024–2025 Security & Agentic Coding Upgrades (REQUIRED)

### 1. AI-Generated Code Security (Modern Best Practices)
- **Human-in-the-loop review:** All AI-generated code must be reviewed by a human before merging or execution. No blind trust in AI output.
- **Automated static and dynamic analysis:** Integrate SAST/DAST tools (e.g., Snyk, SonarQube, CodeQL, OWASP ZAP) into the approval workflow for all AI and user edits.
- **Audit log:** Log all AI actions (file reads, writes, command proposals, tool calls) and user approvals/denials for traceability and compliance.
- **Cursor Rules & R.A.I.L.G.U.A.R.D.:** Support project-level `.cursorrules` and the R.A.I.L.G.U.A.R.D. security framework to guide AI code generation. Validate rules for hidden/unicode attacks. See [CSA R.A.I.L.G.U.A.R.D.](https://cloudsecurityalliance.org/blog/2025/05/06/secure-vibe-coding-level-up-with-cursor-rules-and-the-r-a-i-l-g-u-a-r-d-framework).
- **Input validation & output encoding:** Enforce schema validation (Pydantic, Zod, etc.) and output encoding for all user-facing code.
- **Established libraries only:** Mandate use of proven libraries for auth, session, crypto, CSRF, and security headers. No custom crypto/session code.
- **Secrets management:** Never allow secrets in frontend code. Use environment variables or secret managers. Enable secret scanning (e.g., GitHub, GitGuardian).
- **Dependency scanning:** Run `npm audit`, `pip-audit`, Snyk, or Dependabot on every project and before deployment.
- **Prompt injection & LLM risks:** Integrate [OWASP Top 10 for LLM Applications (2025)](https://owasp.org/www-project-top-10-for-large-language-model-applications/) into agent manager logic and tool-calling.

### 2. Agentic AI & Tooling: Modern Patterns
- **Model Context Protocol (MCP):** Use MCP for agent-tool communication and future multi-agent workflows (see Ardor Cloud, LangChain MCP adapters).
- **Retrieval-Augmented Generation (RAG):** Enable the agent to use project-specific docs, codebase search, and context retrieval for more accurate and secure code generation.
- **Reasoning transparency:** Always display the agent's reasoning, tool selection, and step-by-step plan for every action (not just for models that natively support it).
- **Human-in-the-loop by default:** All file writes and command executions must be explicitly approved by the user, with a clear diff and reasoning shown.

### 3. IDE/UX: Next-Gen Features
- **Security status indicators:** Show real-time security status (e.g., "No vulnerabilities found", "Dependency risk: High", "Secrets detected in code") in the UI.
- **AI action audit log:** Persistent, filterable log of all AI actions, edits, and command proposals, with timestamps and user approvals/denials.
- **Project-level configuration UI:** Allow users to set security policies, AI model restrictions, and custom Cursor Rules from the dashboard.
- **Inline code review tools:** Enable inline code review comments, suggestions, and "request changes" for both AI and user edits.
- **Automated test generation:** Offer AI-powered test generation for new/edited code, with user approval before merging.

### 4. Governance, Compliance, and Education
- **AI Code Usage Policies:** Let users define which models/tools are allowed per project, and restrict AI code in sensitive modules (auth, payments, etc.).
- **Security review checklists:** Provide a checklist for every PR or AI edit, covering input validation, error handling, dependency safety, and more.
- **Developer education:** Integrate security tips, warnings, and links to best practices (e.g., OWASP, CSA) directly in the IDE when risky patterns are detected.
- **Audit trails:** Maintain a full audit trail of all AI-generated code, user approvals, and security scans for compliance and incident response.

### 5. Performance, Scalability, and Extensibility
- **Lazy loading and caching:** Optimize for large projects with lazy file loading and diff caching.
- **Plugin architecture:** Design the agent manager and IDE to support plugins/extensions (e.g., for new models, security tools, or custom workflows).
- **Cloud and local deployment:** Allow both local and cloud-hosted operation, with clear security boundaries and data residency options.

---

## 🔒 Security & Agentic Coding Checklist (for every PR/AI edit)
- [ ] All AI-generated code reviewed by a human
- [ ] SAST/DAST scan run and passed
- [ ] No secrets in frontend code or logs
- [ ] Input validation and output encoding present
- [ ] Only established libraries used for auth/session/crypto
- [ ] Dependency scan (npm audit, pip-audit, Snyk, etc.) run
- [ ] No prompt injection or LLM-specific risks detected
- [ ] Audit log updated with all actions and approvals
- [ ] Security status indicators show "safe"
- [ ] Project-level Cursor Rules and R.A.I.L.G.U.A.R.D. validated

---

## 6. References (2024–2025)
- [OWASP Top 10 for LLM Applications (2025)](https://owasp.org/www-project-top-10-for-large-language-model-applications/)
- [Cloud Security Alliance: Secure Vibe Coding & R.A.I.L.G.U.A.R.D.](https://cloudsecurityalliance.org/blog/2025/05/06/secure-vibe-coding-level-up-with-cursor-rules-and-the-r-a-i-l-g-u-a-r-d-framework)
- [Checkmarx: Securing AI-Generated Code](https://checkmarx.com/blog/ai-is-writing-your-code-whos-keeping-it-secure/)
- [Cased: Fundamentals of Web Security for Vibe Coding](https://cased.com/blog/2025-03-20-fundamentals-of-web-security-for-vibe-coding)
- [Ardor Cloud: Securing AI-Driven Vibe Coding in Production](https://ardor.cloud/blog/security-checks-from-vibe-coding-to-production)

---

## 🤖 Multi-Agent Orchestration Architecture (LangChain/MCP Inspired)

### Overview
Coding Buddy is designed as a **multi-agent system** orchestrated by a central Orchestrator Agent. Each specialized agent is responsible for a distinct class of tasks and is equipped with its own set of tools. This architecture enables parallelism, modularity, and advanced reasoning, following the latest patterns in LangChain, MCP, and agentic AI frameworks.

### Agent Roles
- **Orchestrator Agent:**
  - Receives user requests and decomposes them into subtasks.
  - Delegates subtasks to specialized agents.
  - Aggregates results, manages workflow, and ensures security/approval checkpoints.
- **Coding Agent:**
  - Handles code generation, editing, refactoring, and file operations.
  - Tools: FileOps, Diff, Linter, Formatter, Syntax Checker.
- **Research Agent:**
  - Performs web search, documentation lookup, API retrieval, and context gathering.
  - Tools: WebSearch, Docs, API, Retrieval, RAG.
- **Reasoning Agent:**
  - Tackles complex, ambiguous, or stuck situations with chain-of-thought, multi-step reasoning, and memory.
  - Tools: Chain-of-Thought, RAG, Memory, Self-Reflection.
- **Debug/Error Agent:**
  - Diagnoses errors, runs tests, analyzes logs, and suggests/debugs fixes.
  - Tools: Traceback, TestRunner, LogAnalyzer, Stack Analysis.
- **Ask-User-Questions Agent:**
  - Formulates clarifying questions for the user, processes user answers, and manages dual user-agent dialogue.
  - Tools: Q&A Formulation, UI Feedback, Request Routing.
- **Other Specialized Agents:**
  - (Optional) For plugins, custom workflows, or future extensibility.

### Orchestration Flow
- The Orchestrator Agent routes tasks to the appropriate agent(s) based on task type, context, and current workflow state.
- Agents may call each other or request further research, reasoning, or user input as needed.
- All agent actions are logged and subject to security/approval workflows.
- The system is extensible: new agents/tools can be added with minimal changes to the orchestrator.

### Mermaid Diagram: Multi-Agent Orchestration

```mermaid
flowchart TD
    subgraph OrchestratorAgent["Orchestrator Agent"]
        direction TB
        Orchestrator-->|Delegates coding tasks|CodingAgent
        Orchestrator-->|Delegates research tasks|ResearchAgent
        Orchestrator-->|Delegates reasoning tasks|ReasoningAgent
        Orchestrator-->|Delegates debugging tasks|DebugAgent
        Orchestrator-->|Delegates user Q&A|AskUserAgent
        Orchestrator-->|Other specialized tasks|OtherAgents
    end
    
    subgraph CodingAgent["Coding Agent"]
        CodingTools["Tools: FileOps, Diff, Linter, Formatter"]
    end
    
    subgraph ResearchAgent["Research Agent"]
        ResearchTools["Tools: WebSearch, Docs, API, Retrieval"]
    end
    
    subgraph ReasoningAgent["Reasoning Agent"]
        ReasoningTools["Tools: Chain-of-Thought, RAG, Memory"]
    end
    
    subgraph DebugAgent["Error/Debug Agent"]
        DebugTools["Tools: Traceback, TestRunner, LogAnalyzer"]
    end
    
    subgraph AskUserAgent["Ask-User-Questions Agent"]
        AskUserTools["Tools: Formulate/Process User Qs, UI Feedback"]
    end
    
    subgraph OtherAgents["Other Specialized Agents"]
        OtherTools["Tools: Custom, Plugin, etc."]
    end
    
    CodingAgent-->|Reports results|Orchestrator
    ResearchAgent-->|Reports findings|Orchestrator
    ReasoningAgent-->|Provides insights|Orchestrator
    DebugAgent-->|Reports errors/fixes|Orchestrator
    AskUserAgent-->|User answers/requests|Orchestrator
    OtherAgents-->|Specialized results|Orchestrator
    
    AskUserAgent-->|User input|User["User"]
    User-->|Requests/answers|AskUserAgent
```

### References
- [LangChain Multi-Agent Patterns](https://github.com/langchain-ai/langchain)
- [Model Context Protocol (MCP)](https://ardor.cloud/blog/security-checks-from-vibe-coding-to-production)
- [LangGraph: Agentic Workflow Graphs](https://github.com/langchain-ai/langgraph)

---

## 1. Core Concept

A web-based, IDE-style coding assistant that provides secure, project-isolated AI development support with visual diff editing and command approval workflows.

---

## 2. Technology Stack

### Backend

- **Framework:** [FastAPI](https://fastapi.tiangolo.com/) ([/tiangolo/fastapi][Context7])
  - High performance, async, easy to learn, and production-ready.
  - Serves both API and static frontend files.
  - Hot reload via `uvicorn --reload`.
- **AI Agent Manager:** [LangChain (Python)](https://python.langchain.com/) ([/langchain-ai/langchain][Context7])
  - Enables tool calling for models without native support.
  - Proven agent abstraction for LLM orchestration.
- **AI Model Integration:** [OpenRouter](https://openrouter.ai/) ([/openrouter.ai/llmstxt][Context7])
  - Unified API for multiple models (DeepSeek, Gemini, etc.).
  - Use OpenRouter's API for model switching and runtime selection.
- **Security/Sandboxing:** Custom implementation using FastAPI middleware and Python's file system APIs.
  - Path validation and project isolation.
  - Command whitelisting (see below).

### Frontend

- **Framework:** [React (TypeScript)](https://react.dev/) ([/reactjs/react.dev][Context7])
  - Modern, component-based UI.
  - Hot reload via React dev server.
- **Editor:** [Monaco Editor](https://microsoft.github.io/monaco-editor/) ([/microsoft/monaco-editor][Context7]) + [monaco-react](https://github.com/suren-atoyan/monaco-react) ([/suren-atoyan/monaco-react][Context7])
  - VSCode-like code editing experience.
  - Syntax highlighting for major languages.
- **Diff Viewer:** [React Diff Viewer Continued](https://github.com/aeolun/react-diff-viewer) ([/aeolun/react-diff-viewer-continued][Context7])
  - Side-by-side and inline diff visualization.
- **Terminal:** [xterm.js](https://xtermjs.org/) ([/xtermjs/xterm.js][Context7])
  - Web-based terminal emulator for command preview and logs.
- **File Tree:** [React Complex Tree](https://github.com/lukasbach/react-complex-tree) ([/lukasbach/react-complex-tree][Context7])
  - Accessible, performant file/folder explorer.
- **Tabs:** [React Tabs Draggable](https://github.com/uiwjs/react-tabs-draggable) ([/uiwjs/react-tabs-draggable][Context7])
  - Tabbed file editor interface.
- **Modals:** [Nice Modal React](https://github.com/eBay/nice-modal-react) ([/ebay/nice-modal-react][Context7])
  - For project creation, command approval, etc.
- **Hotkeys:** [React Hotkeys Hook](https://github.com/johannesklauss/react-hotkeys-hook) ([/johannesklauss/react-hotkeys-hook][Context7])
  - Keyboard shortcuts for IDE actions.

---

## 3. AI Integration

### Model Support

- **Primary Models:** DeepSeek R1:0528, Gemini 2.0 Flash Exp (via OpenRouter).
- **Model Switching:** UI dropdown for runtime selection; backend routes requests to selected model.
- **Tool Calling:** 
  - Use LangChain as agent manager for models without native tool support.
  - For models with native tool calling (via OpenRouter), use direct function calling.
  - Fallback: Structured prompting and manual tool detection/execution.

### Reasoning Display

- **Conditional UI:** Only show reasoning section if model supports it (e.g., DeepSeek R1).
- **Expandable Reasoning:** Collapsible UI for reasoning blocks.

---

## 4. Dashboard Layout (IDE-Style)

### Header

- Project name (click-to-rename).
- Project switcher (dropdown).
- New project button (modal).
- Model selector (dropdown).

### File Explorer Panel

- VSCode-style file/folder tree ([React Complex Tree]).
- Controls: refresh, collapse/expand, create file/folder.
- Ultra-thin collapse mode (icons only).
- Security: Only show files within current project root.

### File Editor Panel

- Tabbed interface ([React Tabs Draggable]).
- Monaco Editor for code editing.
- Diff visualization ([React Diff Viewer Continued]).
- Edit approval system:
  - Highlight proposed changes.
  - Accept/Deny buttons for each change block.
  - Preview mode before applying changes.
- Manual editing with syntax highlighting.
- Auto-save on Accept, revert on Deny.

### Terminal Panel

- xterm.js for terminal emulation.
- Command preview before execution.
- Approval dialog (Accept/Deny).
- Command filtering (whitelist).
- Execution log/history.

### Chat Interface

- Action visualization: "Reading main.py...", "Thinking...", etc.
- AI responses: conversational updates, summaries.
- Reasoning section (collapsible, model-dependent).
- User input for requests and chat.

---

## 5. Security Implementation

### Project Isolation

- File system boundary: AI can only access `/projects/{project_id}/`.
- Path validation: Prevent directory traversal.
- Project sandboxing: Each project is isolated.

### Command Safety

- Whitelist approach: Only allow safe commands (`ls`, `cat`, `grep`, `find`, `pip install`, `python`, `npm`, `git`).
- Blocked commands: `rm -rf`, `sudo`, `chmod`, `dd`, system modification commands.
- Approval required: All commands require user confirmation.

### File Operations

- Read: AI can read any file in project.
- Write: AI can propose changes; user must approve.
- No direct write: AI cannot modify files without approval.

---

## 6. Chat Functionality

- Status indicators: Loading spinners, descriptive text.
- Progress updates: Real-time status of AI actions.
- Action categories: Reading, Writing, Thinking, Analyzing, Executing.
- Structured messages: Clear separation of AI responses.
- Code blocks: Syntax-highlighted.
- File references: Clickable links to open files.
- Reasoning integration: Conditional display, expandable sections.

---

## 7. Additional Features

### File Management

- Project templates for quick start.
- Syntax highlighting for major languages.
- Search functionality (find in files).

### Settings & Configuration

- Model configuration: API keys, endpoints, parameters.
- Editor preferences: Theme, font size, tab size.
- Security settings: Command whitelist customization.

### Error Handling

- API failures: Graceful degradation.
- File system errors: Clear error messages.
- Network issues: Offline indicators, retry mechanisms.

### Performance Optimization

- Lazy loading: Load file contents on demand.
- Diff caching: Cache diff calculations for large files.
- Debounced updates: Reduce API calls during rapid interactions.

---

## 8. Development Approach

### Single Codebase Structure

```
coding-buddy/
├── backend/
│   ├── main.py (FastAPI app)
│   ├── agents/ (LangChain integration)
│   ├── security/ (sandboxing, validation)
│   └── projects/ (file operations)
├── frontend/
│   ├── src/
│   │   ├── components/ (React components)
│   │   ├── hooks/ (custom hooks)
│   │   └── services/ (API calls)
│   └── public/
└── projects/ (user project directories)
```

### Development Workflow

1. Backend: FastAPI with hot reload.
2. Frontend: React dev server with proxy to FastAPI.
3. Production: React builds to static files served by FastAPI.
4. No Docker: Direct Python execution for fast iteration.

### Quick Deployment

- Single command: `uvicorn main:app --reload`
- Static serving: FastAPI serves React build from `/static`
- Environment variables: Configuration via `.env`

---

## 9. Library/Component Choices (with Context7 IDs)

- **FastAPI:** `/tiangolo/fastapi`
- **React:** `/reactjs/react.dev`
- **LangChain:** `/langchain-ai/langchain`
- **OpenRouter:** `/openrouter.ai/llmstxt`
- **DeepSeek R1:** `/huggingface/open-r1`
- **Gemini API:** `/hanaokayuzu/gemini-api`
- **Monaco Editor:** `/microsoft/monaco-editor` + `/suren-atoyan/monaco-react`
- **React Diff Viewer:** `/aeolun/react-diff-viewer-continued`
- **xterm.js:** `/xtermjs/xterm.js`
- **React Complex Tree:** `/lukasbach/react-complex-tree`
- **React Tabs Draggable:** `/uiwjs/react-tabs-draggable`
- **Nice Modal React:** `/ebay/nice-modal-react`
- **React Hotkeys Hook:** `/johannesklauss/react-hotkeys-hook`

---

## 10. Additional Recommendations

- **Testing:** Use [React Testing Library](/testing-library/react-testing-library) and [pytest](https://docs.pytest.org/) for robust testing.
- **State Management:** Use React Context or [Redux](/reduxjs/react-redux) for global state.
- **Styling:** Use [Tailwind CSS](https://tailwindcss.com/) or [Chakra UI](https://chakra-ui.com/) for rapid UI development.
- **Documentation:** Inline code comments and a `README.md` for setup and usage.
- **Accessibility:** Ensure all components are accessible (ARIA roles, keyboard navigation).
- **Internationalization:** Optional, for future-proofing.

---

## 11. Implementation Priority

1. Core framework: FastAPI + React setup with static file serving.
2. Basic file operations: Read/write with approval system.
3. AI integration: OpenRouter API with LangChain fallback.
4. Security layer: Project isolation and command filtering.
5. UI components: File explorer, editor, terminal, chat.
6. Advanced features: Diff visualization, reasoning display, templates.

---

## 12. References

- [FastAPI Docs](https://fastapi.tiangolo.com/)
- [React Docs](https://react.dev/)
- [LangChain Docs](https://python.langchain.com/)
- [OpenRouter Docs](https://openrouter.ai/docs)
- [Monaco Editor](https://microsoft.github.io/monaco-editor/)
- [xterm.js](https://xtermjs.org/)
- [React Diff Viewer](https://github.com/aeolun/react-diff-viewer)
- [React Complex Tree](https://github.com/lukasbach/react-complex-tree)
- [React Tabs Draggable](https://github.com/uiwjs/react-tabs-draggable)
- [Nice Modal React](https://github.com/eBay/nice-modal-react)
- [React Hotkeys Hook](https://github.com/johannesklauss/react-hotkeys-hook)

---

**This plan is ready for review and further refinement before implementation.** 