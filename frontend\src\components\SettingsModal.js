import React from 'react';

const SUGGESTED_MODELS = [
  'openrouter/auto',
  'openrouter/4o',
  'openrouter/4o-mini',
  'openrouter/gpt-3.5-turbo',
  'openrouter/deepseek-coder',
  'openrouter/gemini-pro',
  'deepseek/deepseek-r1-0528:free',
  // Add more as desired
];

function sanitizeModelInput(input) {
  // Allow only alphanumeric, dash, slash, colon, dot, underscore
  return input.replace(/[^a-zA-Z0-9\-\/:._]/g, '');
}

export default function SettingsModal({ open, onClose, model, setModel }) {
  if (!open) return null;
  return (
    <div style={{
      position: 'fixed', top: 0, left: 0, width: '100vw', height: '100vh',
      background: 'rgba(0,0,0,0.3)', zIndex: 1000, display: 'flex', alignItems: 'center', justifyContent: 'center'
    }}>
      <div style={{ background: '#fff', borderRadius: 8, padding: 32, minWidth: 350, boxShadow: '0 2px 16px #0002' }}>
        <h2>Settings</h2>
        <div style={{ marginBottom: 24 }}>
          <label style={{ fontWeight: 'bold' }}>AI Model</label><br />
          <input
            type="text"
            value={model}
            onChange={e => setModel(sanitizeModelInput(e.target.value))}
            list="model-suggestions"
            style={{ width: '100%', padding: 8, marginTop: 4 }}
            placeholder="e.g. deepseek/deepseek-r1-0528:free"
            autoComplete="off"
          />
          <datalist id="model-suggestions">
            {SUGGESTED_MODELS.map(m => <option key={m} value={m} />)}
          </datalist>
          <div style={{ color: '#888', fontSize: 13, marginTop: 4 }}>
            Enter any valid OpenRouter model name. See <a href="https://openrouter.ai/models" target="_blank" rel="noopener noreferrer">openrouter.ai/models</a> for options.
          </div>
        </div>
        <div style={{ marginBottom: 24 }}>
          <label style={{ fontWeight: 'bold' }}>Other Settings</label>
          <div style={{ color: '#888', fontSize: 13 }}>More settings coming soon...</div>
        </div>
        <button onClick={onClose} style={{ marginTop: 8, padding: '8px 24px', borderRadius: 4, border: '1px solid #ccc', background: '#f5f5f5', cursor: 'pointer' }}>Close</button>
      </div>
    </div>
  );
} 