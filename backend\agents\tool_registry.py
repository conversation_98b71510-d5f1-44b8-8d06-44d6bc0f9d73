from typing import Callable, Dict
from ..projects import file_ops
from .tools import web_search as web_search_tool
from ..services.chroma_manager import ChromaDBManager
from ..services.indexer import Indexer
from ..services.watcher import start_watching

# Tools that require project_id to be injected
PROJECT_TOOLS = ["read_file", "list_dir", "read_lines", "write_file", "query_codebase"]


def query_codebase(query: str, project_id: str, top_k: int = 5):
    """Search the project's code index for relevant snippets."""
    db = ChromaDBManager(project_id)
    try:
        # If collection is empty, build index first
        collection = db._collection(ChromaDBManager.CODE_COLLECTION)
        if collection.count() == 0:
            idx = Indexer(project_id)
            idx.full_index()
            start_watching(project_id)
        result = db.query_code(query, top_k=top_k)
        # Format result for readability
        matches = []
        documents = (result.get('documents') or [[]])
        metadatas = (result.get('metadatas') or [[]])
        distances = (result.get('distances') or [[]])
        for doc, md, dist in zip(documents[0], metadatas[0], distances[0]):
            matches.append({
                'path': md.get('path', ''),
                'snippet': doc,
                'score': dist,
            })
        return matches
    except Exception as e:
        return f"[query_codebase error] {e}"

def semantic_search_codebase(query: str, project_id: str, top_k: int = 5):
    """Semantic search over the project's codebase using ChromaDB embeddings."""
    db = ChromaDBManager(project_id)
    try:
        result = db.query_code(query, top_k=top_k)
        matches = []
        documents = (result.get('documents') or [[]])
        metadatas = (result.get('metadatas') or [[]])
        distances = (result.get('distances') or [[]])
        for doc, md, dist in zip(documents[0], metadatas[0], distances[0]):
            matches.append({
                'path': md.get('path', ''),
                'snippet': doc,
                'score': dist,
            })
        return matches
    except Exception as e:
        return f"[semantic_search_codebase error] {e}"

def semantic_search_conversation(query: str, project_id: str, top_k: int = 5):
    """Semantic search over the project's conversation history using ChromaDB embeddings."""
    db = ChromaDBManager(project_id)
    try:
        result = db.query_chat(query, top_k=top_k)
        matches = []
        documents = (result.get('documents') or [[]])
        metadatas = (result.get('metadatas') or [[]])
        distances = (result.get('distances') or [[]])
        for doc, md, dist in zip(documents[0], metadatas[0], distances[0]):
            matches.append({
                'role': md.get('role', ''),
                'message': doc,
                'score': dist,
            })
        return matches
    except Exception as e:
        return f"[semantic_search_conversation error] {e}"

# Registry of available tools
TOOLS: Dict[str, Callable] = {
    "read_file": file_ops.read_file,
    "list_dir": file_ops.list_dir,
    "read_lines": file_ops.read_lines,
    "write_file": file_ops.write_file,
    "web_search": web_search_tool.web_search,
    "query_codebase": query_codebase,
    "semantic_search_codebase": semantic_search_codebase,
    "semantic_search_conversation": semantic_search_conversation,
    # Add more tools as needed
}

# Add new tools to PROJECT_TOOLS
PROJECT_TOOLS += ["semantic_search_codebase", "semantic_search_conversation"]

# The orchestrator will not call these directly, but will inject project_id before calling the real function.

def run_tool(tool_name: str, arguments: dict, project_id: str):
    if tool_name not in TOOLS:
        raise ValueError(f"Tool '{tool_name}' not found")

    # If the tool needs project context, inject the project_id.
    if tool_name in PROJECT_TOOLS:
        arguments["project_id"] = project_id
    
    return TOOLS[tool_name](**arguments) 