"""Distillation Agent (Layer-4)

Transforms the running conversation summary (Layer-2) into durable, structured
knowledge and writes it to `<PROJECT_ROOT>/knowledge.json`.
"""
from __future__ import annotations

import json
from pathlib import Path
from typing import Dict

from .openrouter_client import chat_completion
from ..config import PROJECTS_ROOT

DISTILL_PROMPT = (
    "You are an expert technical writer. Given the following conversation\n"
    "summary, extract lasting facts, design decisions, user preferences, and\n"
    "project architecture notes. Return JSON with keys:\n"
    "facts (array), decisions (array), preferences (array), todos (array)."
)


async def distill_summary(project_id: str, summary_text: str, model: str = "openrouter/auto") -> Dict:
    llm_messages = [
        {"role": "system", "content": DISTILL_PROMPT},
        {"role": "user", "content": summary_text},
    ]
    try:
        raw = await chat_completion(llm_messages, model=model)
        data = json.loads(raw)
    except Exception:
        # fallback – wrap summary string
        data = {"raw_summary": summary_text}

    knowledge_path = Path(PROJECTS_ROOT) / project_id / "knowledge.json"
    with open(knowledge_path, "w", encoding="utf-8") as f:
        json.dump(data, f, indent=2)

    return data 