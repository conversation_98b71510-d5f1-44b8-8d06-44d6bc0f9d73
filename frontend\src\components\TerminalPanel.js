import React, { useEffect, useRef, useState } from 'react';
import { Terminal } from 'xterm';
import { runCommand } from '../services/api';
import 'xterm/css/xterm.css';

export default function TerminalPanel() {
  const containerRef = useRef(null);
  const termRef = useRef(null);
  const [currentInput, setCurrentInput] = useState('');
  const [isRunning, setIsRunning] = useState(false);
  const isRunningRef = useRef(false);
  const currentInputRef = useRef('');

  // Keep refs in sync with state
  useEffect(() => {
    isRunningRef.current = isRunning;
  }, [isRunning]);

  useEffect(() => {
    currentInputRef.current = currentInput;
  }, [currentInput]);

  useEffect(() => {
    if (!termRef.current && containerRef.current) {
      const terminal = new Terminal({
        fontSize: 14,
        theme: {
          background: '#1e1e1e',
          foreground: '#ffffff',
          cursor: '#ffffff'
        },
        cursorBlink: true
      });

      termRef.current = terminal;

      // Wait for container to be ready before opening terminal
      setTimeout(() => {
        if (containerRef.current && termRef.current) {
          terminal.open(containerRef.current);
          terminal.writeln('Coding Buddy Terminal');
          terminal.write('> ');

          // Focus the terminal after it's opened
          terminal.focus();
        }
      }, 100);

      // Handle keyboard input - using refs to avoid stale closures
      const handleData = (data) => {
        if (isRunningRef.current) return; // Don't accept input while command is running

        const code = data.charCodeAt(0);

        if (code === 13) { // Enter key
          handleCommand();
        } else if (code === 127) { // Backspace
          if (currentInputRef.current.length > 0) {
            terminal.write('\b \b'); // Move cursor back, write space, move back again
            setCurrentInput(prev => prev.slice(0, -1));
          }
        } else if (code >= 32) { // Printable characters
          terminal.write(data); // Echo the character
          setCurrentInput(prev => prev + data);
        }
      };

      terminal.onData(handleData);
    }

    return () => {
      if (termRef.current) {
        termRef.current.dispose();
        termRef.current = null;
      }
    };
  }, []); // Empty dependency array to prevent re-creation

  const handleCommand = async () => {
    const cmd = currentInputRef.current.trim();

    if (!cmd) {
      termRef.current.write('\r\n> ');
      return;
    }

    termRef.current.write('\r\n'); // New line
    setIsRunning(true);
    setCurrentInput(''); // Clear input immediately

    const ok = window.confirm(`Run command: ${cmd}`);
    if (!ok) {
      termRef.current.write('Command cancelled\r\n> ');
      setIsRunning(false);
      return;
    }

    try {
      termRef.current.writeln(`Running: ${cmd}`);
      const { stdout, stderr } = await runCommand(cmd);

      if (stdout) {
        termRef.current.writeln(stdout);
      }
      if (stderr) {
        termRef.current.writeln(`Error: ${stderr}`);
      }
    } catch (e) {
      termRef.current.writeln(`Error: ${e.message}`);
    } finally {
      termRef.current.write('> ');
      setIsRunning(false);
    }
  };

  return (
    <div
      ref={containerRef}
      style={{
        height: '100%',
        background: '#1e1e1e',
        padding: '8px'
      }}
    />
  );
}