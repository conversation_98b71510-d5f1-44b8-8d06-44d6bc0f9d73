"""ContextAgent

Specialized agent responsible for answering questions about the current
project's codebase by leveraging the ChromaDB-powered index built by the
Indexer service.

NOTE: For now, the agent only supports a single tool call – `search_codebase` –
which accepts a natural-language query and returns the top-k snippets/paths.
The Orchestrator will route calls to this agent once we expose a higher-level
`query_codebase` tool. For the moment, this agent can be instantiated directly
from other agents.
"""
from __future__ import annotations

from typing import Callable, List, Dict
import json
import re

from ..services.chroma_manager import ChromaDBManager
from ..services.openrouter_client import chat_completion

SYSTEM_PROMPT = """
You are the Context Agent. Your job is to search the project's indexed
codebase and return relevant snippets to answer questions from other agents.

Respond with a JSON object. Two possible response shapes:

1. Provide a direct answer (if no search is required):
   {"answer": "...", "thoughts": "why you answered directly"}

2. Request a search:
   {"tool_call": {"tool_name": "search_codebase", "arguments": {"query": "...", "top_k": 5}},
    "thoughts": "why you need search"}
"""

class ContextAgent:
    def __init__(self, project_id: str):
        self.project_id = project_id
        self.db = ChromaDBManager(project_id)

    # ------------------------------------------------------------------
    # Tool – search_codebase
    # ------------------------------------------------------------------

    def search_codebase(self, query: str, top_k: int = 5) -> List[Dict[str, str]]:
        """Return a list of dictionaries with `path`, `snippet`, and `score`."""
        results = self.db.query_code(query, top_k=top_k) or {}
        documents = results.get("documents", [[]])
        metadatas = results.get("metadatas", [[]])
        distances = results.get("distances", [[]])
        matches: List[Dict[str, str]] = []
        if (
            documents
            and metadatas
            and distances
            and len(documents) > 0
            and len(metadatas) > 0
            and len(distances) > 0
        ):
            for doc, md, dist in zip(documents[0], metadatas[0], distances[0]):
                matches.append({
                    "path": str(md.get("path", "")),
                    "snippet": str(doc),
                    "score": str(dist),
                })
        return matches
    # ------------------------------------------------------------------

    async def handle_context_task(self, user_message: str, model: str, tool_runner: Callable):
        yield {"event": "agent_start", "agent": "context"}

        messages = [
            {"role": "system", "content": SYSTEM_PROMPT},
            {"role": "user", "content": user_message},
        ]

        # Single turn for now
        raw = await chat_completion(messages, model=model)

        data = None
        match = re.search(r"\{.*\}", raw, re.DOTALL)
        if match:
            try:
                data = json.loads(match.group(0))
            except json.JSONDecodeError:
                pass

        if not data:
            yield {"event": "final_answer", "answer": raw}
            return

        if data.get("thoughts"):
            yield {"event": "agent_reasoning", "thoughts": data["thoughts"]}

        if "answer" in data and not data.get("tool_call"):
            yield {"event": "final_answer", "answer": data["answer"]}
            return

        if data.get("tool_call"):
            tc = data["tool_call"]
            if tc.get("tool_name") == "search_codebase":
                args = tc.get("arguments", {})
                query = args.get("query")
                top_k = int(args.get("top_k", 5))
                # Determine if user asked for explanation/summary
                explanation_requested = bool(re.search(r"(explain|meaning|what does|purpose|describe|summary)", query or "", re.IGNORECASE))

                # Detect if query looks like a file path; try to read directly first
                direct_answer = None

                # Additional: detect if the user explicitly asked NOT to reveal file contents
                user_wants_no_content = False
                negative_patterns = [
                    r"do\s+not\s+give.*content",
                    r"don't\s+(show|give).*content",
                    r"without\s+.*content",
                    r"no\s+file\s+content",
                ]
                for pat in negative_patterns:
                    if re.search(pat, user_message, re.IGNORECASE):
                        user_wants_no_content = True
                        break

                # Identify candidate path from query
                candidate_path = ""
                if query:
                    match_path = re.search(r"@?(\w[\w\-/]*?\.[\w]+)", query)
                    if match_path:
                        candidate_path = match_path.group(1)

                # Detect pattern "line <number>" in query
                line_match = re.search(r"line\s+(\d+)", query or "", re.IGNORECASE)
                if line_match and candidate_path:
                    line_no = int(line_match.group(1))
                    try:
                        snippet = tool_runner(tool_name="read_lines", arguments={"rel_path": candidate_path, "start_line": line_no, "end_line": line_no})
                        base_line = snippet.strip()
                        # If user requested explanation or meaning in query
                        if explanation_requested:
                            # call LLM to explain
                            messages_ex = [
                                {"role": "system", "content": "You are a helpful assistant who explains short code or text snippets."},
                                {"role": "user", "content": f"Explain this text snippet:\n{base_line}"}
                            ]
                            try:
                                explanation = await chat_completion(messages_ex)
                                direct_answer = f"Line {line_no} of {candidate_path}:\n{base_line}\n\nExplanation:\n{explanation.strip()}"
                            except Exception:
                                direct_answer = f"Line {line_no} of {candidate_path}:\n{base_line}"
                        else:
                            direct_answer = f"Line {line_no} of {candidate_path}:\n{base_line}"
                    except Exception:
                        pass
                elif candidate_path:
                    try:
                        if user_wants_no_content or explanation_requested:
                            # Read file but summarize instead of returning raw content
                            raw_content = tool_runner(tool_name="read_file", arguments={"rel_path": candidate_path})
                            summarize_prompt = [
                                {"role": "system", "content": "Provide a concise description (1-2 sentences) of what this file is for. Do NOT reveal its raw contents."},
                                {"role": "user", "content": raw_content[:12000]},  # safety cap
                            ]
                            summary = await chat_completion(summarize_prompt)
                            direct_answer = f"Summary of {candidate_path}: {summary.strip()}"
                        else:
                            file_content = tool_runner(tool_name="read_file", arguments={"rel_path": candidate_path})
                            direct_answer = f"Contents of {candidate_path}:\n\n{file_content}"
                    except Exception:
                        direct_answer = None

                results = self.search_codebase(query, top_k) if direct_answer is None else []

                # If the query looks like a file path (contains a dot and no spaces) and we found a match, try returning full file instead
                if query and "." in query and " " not in query and results:
                    path = results[0]["path"]
                    try:
                        file_content = tool_runner(tool_name="read_file", arguments={"rel_path": path})
                        answer_str = f"Contents of {path}:\n\n{file_content}"
                    except Exception:
                        answer_str = str(results)
                else:
                    # Convert list of dicts to readable lines
                    lines = [f"{m['path']}:\n{m.get('snippet','')[0:500]}" for m in results]
                    answer_str = "\n\n".join(lines) if lines else "No match found."

                yield {"event": "tool_result", "result": results}
                yield {"event": "final_answer", "answer": direct_answer or answer_str}
                return
            elif tc.get("tool_name") == "semantic_search_codebase":
                args = tc.get("arguments", {})
                query = args.get("query")
                top_k = int(args.get("top_k", 5))
                # Call the semantic search tool
                results = tool_runner(tool_name="semantic_search_codebase", arguments={"query": query, "top_k": top_k})
                # Format results for display
                if isinstance(results, str):
                    answer_str = results
                else:
                    lines = [f"{m['path']} (score: {m['score']:.3f}):\n{m.get('snippet','')[0:500]}" for m in results]
                    answer_str = "\n\n".join(lines) if lines else "No match found."
                yield {"event": "tool_result", "result": results}
                yield {"event": "final_answer", "answer": answer_str}
                return

        yield {"event": "final_answer", "answer": "[ContextAgent] Unable to process request."} 